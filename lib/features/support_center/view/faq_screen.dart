import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/support_center/models/faq.dart';
import 'package:albalad_operator_app/features/support_center/providers.dart';
import 'package:albalad_operator_app/features/support_center/view/expansion_card.dart';
import 'package:albalad_operator_app/features/support_center/view/legal_app_bar.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

bool isFAQLoaded = false;

class FaqScreen extends HookConsumerWidget {
  static const route = '/faq';
  const FaqScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<FAQ>> faqs = ref.watch(faqsProvider);
    final filteredList = useState([]);
    final expandedUid = useState<String?>(null);
    final scrollController = useScrollController();
    final cardKeys = useRef<Map<String, GlobalKey>>({});

    // Auto-scroll function
    void scrollToCard(String faqUid) {
      final key = cardKeys.value[faqUid];
      if (key?.currentContext != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final context = key!.currentContext!;
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null) {
            final position = renderBox.localToGlobal(Offset.zero);
            final screenHeight = MediaQuery.of(context).size.height;

            // Calculate the target scroll position
            // We want to position the card so it's visible with some padding
            final targetPosition =
                scrollController.offset + position.dy - (screenHeight * 0.2);

            scrollController.animateTo(
              targetPosition.clamp(
                  0.0, scrollController.position.maxScrollExtent),
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        });
      }
    }

    return Scaffold(
      appBar: LegalAppBar(
        title: Text(tr(context, 'faqs')),
      ),
      body: faqs.when(
        data: (data) {
          return Builder(
            builder: (context) {
              if (!isFAQLoaded) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  filteredList.value = data;
                });
                isFAQLoaded = true;
              }
              return ListView(
                controller: scrollController,
                padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 20.h),
                children: [
                  HomeSearchField(
                    hintText: tr(context, 'search'),
                    suffixIconVisible: false,
                    onChanged: (p0) {
                      if (p0.isEmpty) {
                        filteredList.value = data;
                      } else {
                        filteredList.value = data
                            .where((element) =>
                                element.question
                                    ?.toLowerCase()
                                    .contains(p0.toLowerCase()) ??
                                false)
                            .toList();
                      }
                      // Clear card keys when filtering changes
                      cardKeys.value.clear();
                      expandedUid.value = null;
                    },
                  ),
                  Gap(30.h),
                  if (filteredList.value.isEmpty)
                    Center(
                      child: Text(tr(context, 'no_data_found')),
                    )
                  else
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const PageScrollPhysics(),
                      itemBuilder: (context, index) {
                        final faq = filteredList.value[index];

                        // Create or get the key for this FAQ
                        if (!cardKeys.value.containsKey(faq.uid)) {
                          cardKeys.value[faq.uid!] = GlobalKey();
                        }

                        return ExpansionCard(
                          key: cardKeys.value[faq.uid],
                          faq: faq,
                          selectedValue: expandedUid.value,
                          onChanged: (value) {
                            if (value == expandedUid.value) {
                              expandedUid.value = null;
                              return;
                            }
                            expandedUid.value = value;

                            // Auto-scroll to the expanded card
                            if (value != null) {
                              scrollToCard(value);
                            }
                          },
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Gap(10.h);
                      },
                      itemCount: filteredList.value.length,
                    ),
                ],
              );
            },
          );
        },
        error: (error, stackTrace) {
          if (error.toString().contains('connection error') ||
              error.toString().contains('connection timeout')) {
            return Center(
              child: NoNetworkWidget(
                onRefresh: () => ref.invalidate(faqsProvider),
              ),
            );
          }
          return const SizedBox();
        },
        loading: () => const Center(
          child: CustomGradientSpinner(),
        ),
      ),
    );
  }
}
