import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_dropdown_field.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SettlementTypeDropdown extends ConsumerWidget {
  final void Function(SettlementType?)? onChanged;
  final SettlementType? value;
  const SettlementTypeDropdown(
      {required this.onChanged, this.value, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settlementType = ref.watch(settlementTypeProvider);
    return settlementType.when(
      data: (values) {
        List<SettlementType> items = values;
        // return CustomSearchableDropdown(
        //   items: items,
        //   displayBuilder: (p0) => p0.name ?? '',
        //   onChanged: onChanged,
        //   labelText: tr(context, 'status_star'),
        //   hintText: tr(context, 'settled'),
        //   title: tr(context, 'status'),
        //   searchHintText: tr(context, 'search_status'),
        //   value: value,
        //   validator: (p0) {
        //     if (p0 == null) {
        //       return tr(context, 'please_select_settlement_status');
        //     }
        //     return null;
        //   },
        // );
        //NORMAL DROPDOWN
        return CustomDropdownField<SettlementType?>(
          items: items
              .map(
                (e) => DropdownMenuItem(value: e, child: Text(e.name ?? '')),
              )
              .toList(),
          onChanged: onChanged,
          label: tr(context, 'status_star'),
          hint: tr(context, 'settled'),
          value: value,
          validator: (p0) {
            if (p0 == null) {
              return tr(context, 'please_select_settlement_status');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
